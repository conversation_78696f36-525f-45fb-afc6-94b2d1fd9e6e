import request from '@/utils/request'

// 获取仪表盘统计数据
export function getDashboardStats() {
  return request({
    url: '/dashboard/stats',
    method: 'get',
    timeout: 10000  // 增加超时时间到10秒
  }).catch(error => {
    console.error('API调用失败:', error)
    // 返回默认数据结构
    return Promise.resolve({
      data: {
        total_machines: 0,
        active_rentals: 0,
        monthly_revenue: 0,
        idle_machines: 0,
        pending_send_orders: 0
      }
    })
  })
}

// 获取最近的租赁记录
export function getRecentRentals(limit = 8) {
  return request({
    url: '/dashboard/recent-rentals',
    method: 'get',
    params: { limit }
  })
}

// 获取趋势数据
export function getTrendData(days = 7) {
  return request({
    url: '/dashboard/trend-data',
    method: 'get',
    params: { days }
  })
}

// 获取待办事项列表
export function getTodoList() {
  return request({
    url: '/dashboard/todo-list',
    method: 'get'
  })
}

// 获取今天应发租赁订单列表
export function getTodayShipOrders() {
  return request({
    url: '/dashboard/today-ship-orders',
    method: 'get'
  })
}

// 获取明天应发租赁订单列表
export function getTomorrowShipOrders() {
  return request({
    url: '/dashboard/tomorrow-ship-orders',
    method: 'get'
  })
}

// 获取催退回订单列表
export function getReturnReminderOrders() {
  return request({
    url: '/dashboard/return-reminder-orders',
    method: 'get'
  })
}