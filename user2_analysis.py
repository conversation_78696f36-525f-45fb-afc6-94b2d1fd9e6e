#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
from datetime import datetime
import pandas as pd

def parse_date(date_str):
    """解析日期字符串"""
    if not date_str or date_str.strip() == '':
        return None
    try:
        # 尝试解析不同的日期格式
        if ' ' in date_str:
            return datetime.strptime(date_str.split()[0], '%Y-%m-%d')
        else:
            return datetime.strptime(date_str, '%Y-%m-%d')
    except:
        return None

def calculate_rental_days(start_date, end_date):
    """计算租赁天数"""
    if not start_date or not end_date:
        return 0
    try:
        start = parse_date(start_date)
        end = parse_date(end_date)
        if start and end:
            return (end - start).days + 1  # 包含开始和结束日期
        return 0
    except:
        return 0

def calculate_shipping_days(send_date, start_date):
    """计算发货到租赁开始的天数（物流时间）"""
    if not send_date or not start_date:
        return 0
    try:
        send = parse_date(send_date)
        start = parse_date(start_date)
        if send and start:
            return (start - send).days
        return 0
    except:
        return 0

def analyze_user2_rentals():
    """分析user_id=2的租赁订单"""
    
    # 读取数据
    rentals = []
    with open('rentals.txt', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f, delimiter='\t')
        for row in reader:
            if row['user_id'] == '2':  # 筛选user_id=2的记录
                rentals.append(row)
    
    print(f"找到 {len(rentals)} 条 user_id=2 的租赁记录\n")
    
    # 分析每个订单
    analysis_results = []
    
    for rental in rentals:
        order_id = rental['id']
        device = rental['device_number']
        customer = rental['customer_name']
        rent_amount = float(rental['rent_amount']) if rental['rent_amount'] else 0
        
        # 计算租赁天数
        rental_days = calculate_rental_days(rental['start_date'], rental['end_date'])
        
        # 计算物流天数
        shipping_days = calculate_shipping_days(rental['send_date'], rental['start_date'])
        
        # 计算日均租金
        daily_rate = rent_amount / rental_days if rental_days > 0 else 0
        
        # 计算"不值"指标
        # 不值程度 = 物流天数 / 租赁天数 * 100 (物流占比越高越不值)
        # 同时考虑日均租金，租金越低越不值
        logistics_ratio = (shipping_days / rental_days * 100) if rental_days > 0 else 0
        
        # 综合不值指数：物流占比 + (1/日均租金)*10 
        # 物流占比高 + 日均租金低 = 更不值
        unworthiness_score = logistics_ratio + (10 / daily_rate if daily_rate > 0 else 100)
        
        analysis_results.append({
            'order_id': order_id,
            'device': device,
            'customer': customer,
            'rent_amount': rent_amount,
            'rental_days': rental_days,
            'shipping_days': shipping_days,
            'daily_rate': daily_rate,
            'logistics_ratio': logistics_ratio,
            'unworthiness_score': unworthiness_score,
            'send_date': rental['send_date'],
            'start_date': rental['start_date'],
            'end_date': rental['end_date'],
            'note': rental['note']
        })
    
    # 按不值程度排序（从高到低）
    analysis_results.sort(key=lambda x: x['unworthiness_score'], reverse=True)
    
    # 输出结果
    print("=" * 100)
    print("USER_ID=2 最不值的租赁订单排序（按不值程度从高到低）")
    print("=" * 100)
    print(f"{'排名':<4} {'订单ID':<6} {'设备':<8} {'客户':<12} {'租金':<6} {'租期':<4} {'物流':<4} {'日均':<6} {'物流占比':<8} {'不值指数':<8} {'备注':<20}")
    print("-" * 100)
    
    for i, result in enumerate(analysis_results, 1):
        print(f"{i:<4} {result['order_id']:<6} {result['device']:<8} {result['customer']:<12} "
              f"{result['rent_amount']:<6.0f} {result['rental_days']:<4} {result['shipping_days']:<4} "
              f"{result['daily_rate']:<6.1f} {result['logistics_ratio']:<8.1f}% {result['unworthiness_score']:<8.1f} "
              f"{result['note'][:20]:<20}")
    
    print("\n" + "=" * 100)
    print("分析说明：")
    print("1. 不值指数 = 物流占比% + (10/日均租金)")
    print("2. 物流占比 = 发货到租赁开始的天数 / 租赁总天数 * 100%")
    print("3. 日均租金 = 总租金 / 租赁天数")
    print("4. 不值指数越高，说明这个订单越不划算")
    print("5. 主要考虑因素：物流时间占比高、日均租金低")
    
    # 输出最不值的前5个订单的详细信息
    print("\n" + "=" * 100)
    print("最不值的前5个订单详细信息：")
    print("=" * 100)
    
    for i, result in enumerate(analysis_results[:5], 1):
        print(f"\n第{i}名 - 订单ID: {result['order_id']}")
        print(f"设备: {result['device']}")
        print(f"客户: {result['customer']}")
        print(f"发货日期: {result['send_date']}")
        print(f"租赁期间: {result['start_date']} 到 {result['end_date']}")
        print(f"租金: {result['rent_amount']}元")
        print(f"租赁天数: {result['rental_days']}天")
        print(f"物流天数: {result['shipping_days']}天")
        print(f"日均租金: {result['daily_rate']:.1f}元/天")
        print(f"物流占比: {result['logistics_ratio']:.1f}%")
        print(f"不值指数: {result['unworthiness_score']:.1f}")
        print(f"备注: {result['note']}")
        print("-" * 50)

if __name__ == "__main__":
    analyze_user2_rentals()
